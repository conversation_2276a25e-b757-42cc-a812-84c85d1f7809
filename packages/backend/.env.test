# Test Environment Configuration
NODE_ENV=test
PORT=3001

# Test Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/apotek_test"

# JWT Configuration for Tests
JWT_SECRET="test-jwt-secret-key-for-integration-tests-change-in-production"
JWT_EXPIRES_IN="1h"

# Disable throttling for tests (or use very high limits)
THROTTLE_TTL=60000
THROTTLE_LIMIT=1000

# Test-specific settings
LOG_LEVEL=error
DISABLE_RATE_LIMITING=true

# Logging Configuration for Tests
# Disable Logtail in test environment
# LOGTAIL_TOKEN=""
