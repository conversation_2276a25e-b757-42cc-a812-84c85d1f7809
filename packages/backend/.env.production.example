# Production Environment Configuration Example
NODE_ENV=production
PORT=3001

# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-for-production"
JWT_EXPIRES_IN="7d"

# Throttling Configuration
THROTTLE_TTL=60000
THROTTLE_LIMIT=10

# Logging Configuration
LOG_LEVEL="info"
LOGTAIL_TOKEN="your-betterstack-logtail-token-here"
