import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { BigIntSerializerInterceptor, GlobalExceptionFilter } from './common/utils';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);

    // Global exception filter for 500 errors and comprehensive error handling
    app.useGlobalFilters(new GlobalExceptionFilter());

    // Enable CORS
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://127.0.0.1:3000'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // Apply BigInt serializer interceptor globally
    app.useGlobalInterceptors(new BigIntSerializerInterceptor());

    // Global prefix
    app.setGlobalPrefix('api');

    const port = configService.get<number>('PORT') || 3001;

    await app.listen(port);

    logger.log(`🚀 Backend server berhasil berjalan di http://localhost:${port}/api`);
    logger.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`📊 Global exception filter aktif untuk error handling`);
    logger.log(`🛡️ Global validation pipe aktif`);
    logger.log(`🔄 BigInt serializer interceptor aktif`);

  } catch (error) {
    logger.error('❌ Gagal memulai server:', error);
    process.exit(1);
  }
}

// Global error handlers for unhandled rejections and uncaught exceptions
process.on('unhandledRejection', (reason, promise) => {
  const logger = new Logger('UnhandledRejection');
  logger.error('🚨 Unhandled Promise Rejection:', {
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : undefined,
    promise: promise.toString(),
    timestamp: new Date().toISOString(),
  });

  // In production, you might want to gracefully shutdown
  if (process.env.NODE_ENV === 'production') {
    logger.error('🔄 Gracefully shutting down due to unhandled rejection...');
    process.exit(1);
  }
});

process.on('uncaughtException', (error) => {
  const logger = new Logger('UncaughtException');
  logger.error('🚨 Uncaught Exception:', {
    message: error.message,
    stack: error.stack,
    name: error.name,
    timestamp: new Date().toISOString(),
  });

  // Always exit on uncaught exceptions
  logger.error('🔄 Shutting down due to uncaught exception...');
  process.exit(1);
});

// Graceful shutdown handlers
process.on('SIGTERM', () => {
  const logger = new Logger('Shutdown');
  logger.log('🔄 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  const logger = new Logger('Shutdown');
  logger.log('🔄 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

bootstrap();
