import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Params } from 'nestjs-pino';
import { Logtail } from '@logtail/node';
import PinoLogtail from '@logtail/pino';
import pino, { LoggerOptions } from 'pino';

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  level: string;
  environment: string;
  enableLogtail: boolean;
  logtailToken?: string;
  prettyPrint: boolean;
  redact: string[];
}

/**
 * Logger configuration service for Pino with Betterstack Logtail integration
 */
@Injectable()
export class LoggerConfigService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * Get logger configuration based on environment
   */
  getLoggerConfig(): LoggerConfig {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    const isProduction = environment === 'production';
    const isTest = environment === 'test';

    return {
      level: this.configService.get<string>('LOG_LEVEL', isTest ? 'error' : isProduction ? 'info' : 'debug'),
      environment,
      enableLogtail: isProduction && !!this.configService.get<string>('LOGTAIL_TOKEN'),
      logtailToken: this.configService.get<string>('LOGTAIL_TOKEN'),
      prettyPrint: !isProduction && !isTest,
      redact: [
        'password',
        'token',
        'authorization',
        'cookie',
        'secret',
        'key',
        'req.headers.authorization',
        'req.headers.cookie',
        'res.headers["set-cookie"]',
      ],
    };
  }

  /**
   * Create Pino logger options for NestJS
   */
  createPinoOptions(): Params {
    const config = this.getLoggerConfig();

    // Base logger options
    const pinoOptions: pino.LoggerOptions = {
      level: config.level as pino.Level,
      redact: config.redact,
      serializers: {
        req: (req) => ({
          id: req.id,
          method: req.method,
          url: req.url,
          headers: {
            host: req.headers?.host,
            'user-agent': req.headers?.['user-agent'],
            'content-type': req.headers?.['content-type'],
          },
          remoteAddress: req.remoteAddress,
          remotePort: req.remotePort,
        }),
        res: (res) => ({
          statusCode: res.statusCode,
          headers: {
            'content-type': res.headers?.['content-type'],
            'content-length': res.headers?.['content-length'],
          },
        }),
        err: pino.stdSerializers.err,
      },
      formatters: {
        level: (label) => ({ level: label }),
        log: (object) => ({
          ...object,
          environment: config.environment,
          service: 'pharmacy-backend',
          timestamp: new Date().toISOString(),
        }),
      },
    };

    // Development stream with pretty printing
    if (config.prettyPrint) {
      streams.push({
        level: config.level,
        stream: pino.destination({
          dest: 1, // stdout
          sync: false,
        }),
        options: {
          ...baseOptions,
          transport: {
            target: 'pino-pretty',
            options: {
              colorize: true,
              translateTime: 'yyyy-mm-dd HH:MM:ss',
              ignore: 'pid,hostname',
              singleLine: false,
              hideObject: false,
            },
          },
        },
      });
    }

    // Production stream
    if (!config.prettyPrint) {
      streams.push({
        level: config.level,
        stream: pino.destination({
          dest: 1, // stdout
          sync: false,
        }),
        options: baseOptions,
      });
    }

    // Betterstack Logtail stream for production
    if (config.enableLogtail && config.logtailToken) {
      try {
        const logtail = new Logtail(config.logtailToken);
        const logtailStream = new PinoLogtail(logtail);

        streams.push({
          level: config.level,
          stream: logtailStream,
          options: {
            ...baseOptions,
            formatters: {
              ...baseOptions.formatters,
              log: (object) => ({
                ...object,
                environment: config.environment,
                service: 'pharmacy-backend',
                timestamp: new Date().toISOString(),
                source: 'nestjs-backend',
              }),
            },
          },
        });
      } catch (error) {
        console.error('Failed to initialize Logtail stream:', error);
      }
    }

    return {
      pinoHttp: {
        logger: streams.length > 1 
          ? pino(baseOptions, pino.multistream(streams))
          : pino(streams[0]?.options || baseOptions, streams[0]?.stream),
        autoLogging: true,
        quietReqLogger: config.environment === 'test',
        customLogLevel: (req, res, err) => {
          if (res.statusCode >= 400 && res.statusCode < 500) {
            return 'warn';
          } else if (res.statusCode >= 500 || err) {
            return 'error';
          }
          return 'info';
        },
        customSuccessMessage: (req, res) => {
          return `${req.method} ${req.url} - ${res.statusCode}`;
        },
        customErrorMessage: (req, res, err) => {
          return `${req.method} ${req.url} - ${res.statusCode} - ${err.message}`;
        },
        customAttributeKeys: {
          req: 'request',
          res: 'response',
          err: 'error',
          responseTime: 'duration',
        },
      },
    };
  }

  /**
   * Create standalone Pino logger instance
   */
  createStandaloneLogger(context?: string): pino.Logger {
    const config = this.getLoggerConfig();
    const streams: pino.StreamEntry[] = [];

    const baseOptions: pino.LoggerOptions = {
      level: config.level,
      redact: config.redact,
      base: {
        service: 'pharmacy-backend',
        environment: config.environment,
        context: context || 'Application',
      },
      serializers: {
        err: pino.stdSerializers.err,
      },
      formatters: {
        level: (label) => ({ level: label }),
        log: (object) => ({
          ...object,
          timestamp: new Date().toISOString(),
        }),
      },
    };

    // Development stream
    if (config.prettyPrint) {
      return pino({
        ...baseOptions,
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'yyyy-mm-dd HH:MM:ss',
            ignore: 'pid,hostname',
          },
        },
      });
    }

    // Production streams
    streams.push({
      level: config.level,
      stream: pino.destination({ dest: 1, sync: false }),
      options: baseOptions,
    });

    // Add Logtail stream if configured
    if (config.enableLogtail && config.logtailToken) {
      try {
        const logtail = new Logtail(config.logtailToken);
        const logtailStream = new PinoLogtail(logtail);

        streams.push({
          level: config.level,
          stream: logtailStream,
          options: baseOptions,
        });
      } catch (error) {
        console.error('Failed to initialize Logtail for standalone logger:', error);
      }
    }

    return streams.length > 1
      ? pino(baseOptions, pino.multistream(streams))
      : pino(baseOptions, streams[0]?.stream);
  }
}

/**
 * Factory function to create logger options
 */
export const createLoggerOptions = (configService: ConfigService): Params => {
  const loggerConfigService = new LoggerConfigService(configService);
  return loggerConfigService.createPinoOptions();
};

/**
 * Factory function to create standalone logger
 */
export const createStandaloneLogger = (configService: ConfigService, context?: string): pino.Logger => {
  const loggerConfigService = new LoggerConfigService(configService);
  return loggerConfigService.createStandaloneLogger(context);
};
