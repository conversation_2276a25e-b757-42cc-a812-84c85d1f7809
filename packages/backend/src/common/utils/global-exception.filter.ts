import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Prisma } from '@prisma/client';

/**
 * Global exception filter to handle all unhandled exceptions
 * Provides consistent error responses and comprehensive logging
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const { status, message, errorCode } = this.getErrorDetails(exception);

    // Log error details
    this.logError(exception, request, status);

    // Send error response
    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message,
      errorCode,
      ...(process.env.NODE_ENV === 'development' && {
        stack: exception instanceof Error ? exception.stack : undefined,
      }),
    };

    response.status(status).json(errorResponse);
  }

  private getErrorDetails(exception: unknown): {
    status: number;
    message: string;
    errorCode?: string;
  } {
    // Handle HTTP exceptions
    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      return {
        status: exception.getStatus(),
        message: typeof response === 'string' ? response : (response as any).message || exception.message,
        errorCode: 'HTTP_EXCEPTION',
      };
    }

    // Handle Prisma errors
    if (exception instanceof Prisma.PrismaClientKnownRequestError) {
      return this.handlePrismaError(exception);
    }

    if (exception instanceof Prisma.PrismaClientUnknownRequestError) {
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Terjadi kesalahan database yang tidak diketahui',
        errorCode: 'PRISMA_UNKNOWN_ERROR',
      };
    }

    if (exception instanceof Prisma.PrismaClientRustPanicError) {
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Terjadi kesalahan sistem database',
        errorCode: 'PRISMA_PANIC_ERROR',
      };
    }

    if (exception instanceof Prisma.PrismaClientInitializationError) {
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Gagal menginisialisasi koneksi database',
        errorCode: 'PRISMA_INIT_ERROR',
      };
    }

    if (exception instanceof Prisma.PrismaClientValidationError) {
      return {
        status: HttpStatus.BAD_REQUEST,
        message: 'Data yang dikirim tidak valid',
        errorCode: 'PRISMA_VALIDATION_ERROR',
      };
    }

    // Handle other known errors
    if (exception instanceof Error) {
      // Handle specific error types
      if (exception.name === 'ValidationError') {
        return {
          status: HttpStatus.BAD_REQUEST,
          message: exception.message || 'Data validasi gagal',
          errorCode: 'VALIDATION_ERROR',
        };
      }

      if (exception.name === 'UnauthorizedError') {
        return {
          status: HttpStatus.UNAUTHORIZED,
          message: 'Akses tidak diizinkan',
          errorCode: 'UNAUTHORIZED_ERROR',
        };
      }

      if (exception.name === 'ForbiddenError') {
        return {
          status: HttpStatus.FORBIDDEN,
          message: 'Akses ditolak',
          errorCode: 'FORBIDDEN_ERROR',
        };
      }

      if (exception.name === 'NotFoundError') {
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Data tidak ditemukan',
          errorCode: 'NOT_FOUND_ERROR',
        };
      }
    }

    // Default to 500 for unknown errors
    return {
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Terjadi kesalahan internal server',
      errorCode: 'INTERNAL_SERVER_ERROR',
    };
  }

  private handlePrismaError(error: Prisma.PrismaClientKnownRequestError): {
    status: number;
    message: string;
    errorCode: string;
  } {
    switch (error.code) {
      case 'P2000':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Data yang diberikan terlalu panjang untuk kolom database',
          errorCode: 'PRISMA_P2000',
        };
      case 'P2001':
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Data yang dicari tidak ditemukan',
          errorCode: 'PRISMA_P2001',
        };
      case 'P2002':
        return {
          status: HttpStatus.CONFLICT,
          message: 'Data sudah ada, tidak dapat membuat duplikat',
          errorCode: 'PRISMA_P2002',
        };
      case 'P2003':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Referensi data tidak valid',
          errorCode: 'PRISMA_P2003',
        };
      case 'P2004':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Constraint database dilanggar',
          errorCode: 'PRISMA_P2004',
        };
      case 'P2005':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Nilai field tidak valid untuk tipe data',
          errorCode: 'PRISMA_P2005',
        };
      case 'P2006':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Nilai yang diberikan tidak valid',
          errorCode: 'PRISMA_P2006',
        };
      case 'P2007':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Kesalahan validasi data',
          errorCode: 'PRISMA_P2007',
        };
      case 'P2008':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Gagal parsing query database',
          errorCode: 'PRISMA_P2008',
        };
      case 'P2009':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Gagal validasi query database',
          errorCode: 'PRISMA_P2009',
        };
      case 'P2010':
        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Query database gagal dieksekusi',
          errorCode: 'PRISMA_P2010',
        };
      case 'P2011':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Constraint null dilanggar',
          errorCode: 'PRISMA_P2011',
        };
      case 'P2012':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Nilai yang diperlukan tidak ada',
          errorCode: 'PRISMA_P2012',
        };
      case 'P2013':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Argumen yang diperlukan tidak ada',
          errorCode: 'PRISMA_P2013',
        };
      case 'P2014':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Perubahan akan melanggar relasi yang diperlukan',
          errorCode: 'PRISMA_P2014',
        };
      case 'P2015':
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Record terkait tidak ditemukan',
          errorCode: 'PRISMA_P2015',
        };
      case 'P2016':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Kesalahan interpretasi query',
          errorCode: 'PRISMA_P2016',
        };
      case 'P2017':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Record tidak terhubung',
          errorCode: 'PRISMA_P2017',
        };
      case 'P2018':
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Record yang diperlukan tidak ditemukan',
          errorCode: 'PRISMA_P2018',
        };
      case 'P2019':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Kesalahan input',
          errorCode: 'PRISMA_P2019',
        };
      case 'P2020':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Nilai di luar rentang',
          errorCode: 'PRISMA_P2020',
        };
      case 'P2021':
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Tabel tidak ditemukan dalam database',
          errorCode: 'PRISMA_P2021',
        };
      case 'P2022':
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Kolom tidak ditemukan dalam database',
          errorCode: 'PRISMA_P2022',
        };
      case 'P2023':
        return {
          status: HttpStatus.BAD_REQUEST,
          message: 'Data kolom tidak konsisten',
          errorCode: 'PRISMA_P2023',
        };
      case 'P2024':
        return {
          status: HttpStatus.REQUEST_TIMEOUT,
          message: 'Timeout koneksi ke database',
          errorCode: 'PRISMA_P2024',
        };
      case 'P2025':
        return {
          status: HttpStatus.NOT_FOUND,
          message: 'Operasi gagal karena record yang diperlukan tidak ditemukan',
          errorCode: 'PRISMA_P2025',
        };
      default:
        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Terjadi kesalahan database',
          errorCode: `PRISMA_${error.code}`,
        };
    }
  }

  private logError(exception: unknown, request: Request, status: number): void {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
      status,
      message: exception instanceof Error ? exception.message : 'Unknown error',
      stack: exception instanceof Error ? exception.stack : undefined,
    };

    if (status >= 500) {
      this.logger.error(
        `🚨 Server Error [${status}] ${request.method} ${request.url}`,
        {
          ...errorInfo,
          exception: exception instanceof Error ? exception.name : typeof exception,
        }
      );
    } else if (status >= 400) {
      this.logger.warn(
        `⚠️ Client Error [${status}] ${request.method} ${request.url}`,
        {
          ...errorInfo,
          exception: exception instanceof Error ? exception.name : typeof exception,
        }
      );
    } else {
      this.logger.log(
        `ℹ️ Request [${status}] ${request.method} ${request.url}`,
        errorInfo
      );
    }
  }
}
