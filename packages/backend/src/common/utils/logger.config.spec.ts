import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LoggerConfigService, createLoggerOptions, createStandaloneLogger } from './logger.config';

describe('LoggerConfigService', () => {
  let service: LoggerConfigService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggerConfigService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                NODE_ENV: 'test',
                LOG_LEVEL: 'debug',
                LOGTAIL_TOKEN: 'test-token',
                LOGTAIL_ENDPOINT: 'https://test.logtail.com',
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<LoggerConfigService>(LoggerConfigService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getLoggerConfig', () => {
    it('should return correct config for test environment', () => {
      const config = service.getLoggerConfig();

      expect(config.level).toBe('debug');
      expect(config.environment).toBe('test');
      expect(config.enableLogtail).toBe(false); // Should be false in test
      expect(config.prettyPrint).toBe(false); // Should be false in test
      expect(config.redact).toContain('password');
      expect(config.redact).toContain('token');
    });

    it('should return correct config for development environment', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'development',
          LOG_LEVEL: 'debug',
        };
        return config[key] ?? defaultValue;
      });

      const config = service.getLoggerConfig();

      expect(config.level).toBe('debug');
      expect(config.environment).toBe('development');
      expect(config.enableLogtail).toBe(false); // No token in dev
      expect(config.prettyPrint).toBe(true); // Should be true in dev
    });

    it('should return correct config for production environment', () => {
      const mockConfigService = {
        get: jest.fn((key: string, defaultValue?: any) => {
          const config: Record<string, any> = {
            NODE_ENV: 'production',
            LOG_LEVEL: 'info',
            LOGTAIL_TOKEN: 'prod-token',
            LOGTAIL_ENDPOINT: 'https://in.logtail.com',
          };
          return config[key] ?? defaultValue;
        }),
      } as any;

      const testService = new LoggerConfigService(mockConfigService);
      const config = testService.getLoggerConfig();

      expect(config.level).toBe('info');
      expect(config.environment).toBe('production');
      expect(config.enableLogtail).toBe(true); // Should be true with token
      expect(config.logtailToken).toBe('prod-token');
      expect(config.logtailEndpoint).toBe('https://in.logtail.com');
      expect(config.prettyPrint).toBe(false); // Should be false in prod
    });
  });

  describe('createPinoOptions', () => {
    it('should create correct options for development', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'development',
          LOG_LEVEL: 'debug',
        };
        return config[key] ?? defaultValue;
      });

      const options = service.createPinoOptions();

      expect(options.pinoHttp).toBeDefined();
      expect((options.pinoHttp as any).level).toBe('debug');
      expect((options.pinoHttp as any).transport).toBeDefined();
      expect((options.pinoHttp as any).transport.target).toBe('pino-pretty');
    });

    it('should create correct options for production with Logtail', () => {
      const mockConfigService = {
        get: jest.fn((key: string, defaultValue?: any) => {
          const config: Record<string, any> = {
            NODE_ENV: 'production',
            LOG_LEVEL: 'info',
            LOGTAIL_TOKEN: 'prod-token',
            LOGTAIL_ENDPOINT: 'https://in.logtail.com',
          };
          return config[key] ?? defaultValue;
        }),
      } as any;

      const testService = new LoggerConfigService(mockConfigService);
      const options = testService.createPinoOptions();

      expect(options.pinoHttp).toBeDefined();
      expect((options.pinoHttp as any).level).toBe('info');
      expect((options.pinoHttp as any).transport).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toHaveLength(2);

      const logtailTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === '@logtail/pino'
      );
      expect(logtailTarget).toBeDefined();
      expect(logtailTarget.options.sourceToken).toBe('prod-token');
      expect(logtailTarget.options.options).toBeDefined();
      expect(logtailTarget.options.options.endpoint).toBe('https://in.logtail.com');
    });

    it('should create correct options for production without Logtail', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'production',
          LOG_LEVEL: 'info',
        };
        return config[key] ?? defaultValue;
      });

      const options = service.createPinoOptions();

      expect(options.pinoHttp).toBeDefined();
      expect((options.pinoHttp as any).transport).toBeDefined();
      expect((options.pinoHttp as any).transport.target).toBe('pino/file');
      expect((options.pinoHttp as any).transport.options.destination).toBe(1); // stdout
    });
  });

  describe('createStandaloneLogger', () => {
    it('should create logger with context', () => {
      const logger = service.createStandaloneLogger('TestContext');
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });

    it('should create logger without context', () => {
      const logger = service.createStandaloneLogger();
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
    });
  });
});

describe('Factory Functions', () => {
  let configService: ConfigService;

  beforeEach(() => {
    configService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'test',
          LOG_LEVEL: 'debug',
        };
        return config[key] ?? defaultValue;
      }),
    } as any;
  });

  describe('createLoggerOptions', () => {
    it('should create logger options using factory function', () => {
      const options = createLoggerOptions(configService);
      
      expect(options).toBeDefined();
      expect(options.pinoHttp).toBeDefined();
    });
  });

  describe('createStandaloneLogger', () => {
    it('should create standalone logger using factory function', () => {
      const logger = createStandaloneLogger(configService, 'FactoryTest');
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
    });
  });
});
